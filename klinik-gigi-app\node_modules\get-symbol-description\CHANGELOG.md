# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/inspect-js/get-symbol-description/compare/v1.0.2...v1.1.0) - 2024-12-17

### Commits

- [New] add types [`b957b65`](https://github.com/inspect-js/get-symbol-description/commit/b957b65e08bc1a6ac95fa5ab769ec241b9cac885)
- [actions] split out node 10-20, and 20+ [`bfbcae2`](https://github.com/inspect-js/get-symbol-description/commit/bfbcae2ab7224fcf4328bc139ba79445d64030a6)
- [<PERSON><PERSON>] update `@ljharb/eslint-config`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, tape` [`197ba80`](https://github.com/inspect-js/get-symbol-description/commit/197ba80ef87153e28f20ec353e8b926ddb145da0)
- [Refactor] use `call-bound` directly [`9df4de4`](https://github.com/inspect-js/get-symbol-description/commit/9df4de4e8faae09e84c5ac97ec22b4f010d03fca)
- [Deps] update `call-bind`, `get-intrinsic` [`44c1400`](https://github.com/inspect-js/get-symbol-description/commit/44c1400d5088429f6a32a5f81628d9f7270f68ca)
- [Dev Deps] update `hasown`, `tape` [`44e2264`](https://github.com/inspect-js/get-symbol-description/commit/44e226470a83e89523bb4898e1ef7a0942e6cb3a)
- [Tests] replace `aud` with `npm audit` [`62d9414`](https://github.com/inspect-js/get-symbol-description/commit/62d9414d316f7ba2320cb6fad8d9fd4d8b99c420)
- [Deps] update `call-bind` [`396ee27`](https://github.com/inspect-js/get-symbol-description/commit/396ee2763238415c51eec62fbc41bf274c6552b2)
- [Dev Deps] add missing peer dep [`cc4b9eb`](https://github.com/inspect-js/get-symbol-description/commit/cc4b9eb527504a49e91f560ee6d9cb942db4e46f)

## [v1.0.2](https://github.com/inspect-js/get-symbol-description/compare/v1.0.1...v1.0.2) - 2024-02-07

### Fixed

- [Deps] add missing `get-intrinsic` [`#3`](https://github.com/inspect-js/get-symbol-description/issues/3)

## [v1.0.1](https://github.com/inspect-js/get-symbol-description/compare/v1.0.0...v1.0.1) - 2024-02-05

### Commits

- [actions] reuse common workflows [`168adf2`](https://github.com/inspect-js/get-symbol-description/commit/168adf213f86e5c69a93b4768a20ad543a70b231)
- [meta] use `npmignore` to autogenerate an npmignore file [`fa3b323`](https://github.com/inspect-js/get-symbol-description/commit/fa3b323f0605cf966a5cef1a103ada46d63e466b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `es-value-fixtures`, `foreach`, `object-inspect`, `tape` [`9301b9e`](https://github.com/inspect-js/get-symbol-description/commit/9301b9e274fd9b7544af3d7d437dd254e83095e0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`a92a011`](https://github.com/inspect-js/get-symbol-description/commit/a92a0119f373fb61c58e3eb1d5fb6b3a3f66f157)
- [actions] update rebase action to use reusable workflow [`66cea29`](https://github.com/inspect-js/get-symbol-description/commit/66cea29835bc88ab5e937ccf996ea96409475a0e)
- [actions] update codecov uploader [`84079e1`](https://github.com/inspect-js/get-symbol-description/commit/84079e12e1421a79b63757cc3ab9c599e8eecc75)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`9f298a5`](https://github.com/inspect-js/get-symbol-description/commit/9f298a521e6f8a9b974b6b95e0b3de8aeaf74d9c)
- [Dev Deps] use `hasown` instead of `has` [`e993bd6`](https://github.com/inspect-js/get-symbol-description/commit/e993bd62a08a1adc2f75664be99a36e031ecf604)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`5044bed`](https://github.com/inspect-js/get-symbol-description/commit/5044bed49a1b2b529b0c92fee0504747fda78147)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`3923eab`](https://github.com/inspect-js/get-symbol-description/commit/3923eabcf3eb2ddad7dbfd542102c29646dac242)
- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`a24f5c5`](https://github.com/inspect-js/get-symbol-description/commit/a24f5c5f6ddd1f24b22ecdc2546eb9b06924f62a)
- [Deps] update `call-bind`, `get-intrinsic` [`accd484`](https://github.com/inspect-js/get-symbol-description/commit/accd484cb970c11fb39eb5ec4301572fa4043e37)
- [Dev Deps] update `object-inspect`, `tape` [`6c66623`](https://github.com/inspect-js/get-symbol-description/commit/6c666237114333bcb548e2c9ba6eb4924cb154ad)
- [Dev Deps] update `object-inspect`, `tape` [`586dfe3`](https://github.com/inspect-js/get-symbol-description/commit/586dfe35b9b6e7dba3fb7577c5973b7466d101a3)
- [Dev Deps] update `@ljharb/eslint-config`, `aud` [`bc8c7e0`](https://github.com/inspect-js/get-symbol-description/commit/bc8c7e0382682164f78b87f41764a0a2e389c435)
- [Tests] use `for-each` instead of `foreach` [`ca97918`](https://github.com/inspect-js/get-symbol-description/commit/ca97918eaad4ff1df11fd6f187da60227722dfcd)
- [Robustness] cache String slice [`5ce0c56`](https://github.com/inspect-js/get-symbol-description/commit/5ce0c5658224ed5cf5c6775a18ee2ad60c5b7ba8)
- [Deps] update `get-intrinsic` [`b656c5c`](https://github.com/inspect-js/get-symbol-description/commit/b656c5c68fbeec35d75a635ca991b61ed004bf54)
- [Deps] update `get-intrinsic` [`74cf3b6`](https://github.com/inspect-js/get-symbol-description/commit/74cf3b6525c49998f2c984d350e4d59d7f70794c)
- [meta] fix FUNDING.yml [`6cf76c8`](https://github.com/inspect-js/get-symbol-description/commit/6cf76c8c56bf366f767a84e82038db54b508641a)

## v1.0.0 - 2021-08-17

### Commits

- Initial commit: pulled from es-abstract [`6e34a05`](https://github.com/inspect-js/get-symbol-description/commit/6e34a05ef10ce8620078cf4cecbe276c1fc1ae77)
- Initial commit [`3862092`](https://github.com/inspect-js/get-symbol-description/commit/3862092248d8ffa071ec90ec39d73e8be14ba6f1)
- [meta] do not publish github action workflow files [`9d1e2b9`](https://github.com/inspect-js/get-symbol-description/commit/9d1e2b94dd97664da5d0666985a3695c23f45865)
- npm init [`5051b32`](https://github.com/inspect-js/get-symbol-description/commit/5051b3221829f364c44b4d5e9a0c35aab3247f6a)
- Only apps should have lockfiles [`b866d1c`](https://github.com/inspect-js/get-symbol-description/commit/b866d1c4b4029277618d968cfb3cbe00f012d1a7)

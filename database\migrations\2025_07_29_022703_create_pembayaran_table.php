<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pembayaran', function (Blueprint $table) {
            $table->id('id_pembayaran');
            $table->foreignId('id_tagihan')->constrained('tagihan', 'id_tagihan');
            $table->string('nomor_pembayaran')->unique();
            $table->date('tanggal_pembayaran');
            $table->decimal('jumlah_pembayaran', 12, 2);
            $table->enum('metode_pembayaran', ['tunai', 'transfer', 'kartu_kredit', 'kartu_debit', 'bpjs', 'asuransi']);
            $table->string('referensi_pembayaran')->nullable()->comment('Nomor referensi bank/kartu');
            $table->text('catatan')->nullable();
            $table->foreignId('diterima_oleh')->constrained('pengguna', 'id_pengguna');
            $table->timestamps();
            
            // Indexes
            $table->index(['id_tagihan', 'tanggal_pembayaran']);
            $table->index('nomor_pembayaran');
            $table->index(['metode_pembayaran', 'tanggal_pembayaran']);
            $table->index('diterima_oleh');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pembayaran');
    }
};

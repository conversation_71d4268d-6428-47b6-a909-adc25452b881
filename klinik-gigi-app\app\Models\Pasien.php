<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;

class Pasien extends Model
{
    use HasFactory;

    protected $table = 'pasien';
    protected $primaryKey = 'id_pasien';

    protected $fillable = [
        'nomor_pasien',
        'nama_depan',
        'nama_belakang',
        'tanggal_lahir',
        'jenis_kelamin',
        'no_telepon',
        'email',
        'alamat',
        'kota',
        'kode_pos',
        'provinsi',
        'kabupaten_kota',
        'kecamatan',
        'kelurahan_desa',
        'rt_rw',
        'nik',
        'nama_kontak_darurat',
        'telepon_kontak_darurat',
        'penyedia_asuransi',
        'nomor_asuransi',
        'nomor_bpjs',
        'kelas_bpjs',
        'status_kepesertaan_bpjs',
        'faskes_tingkat_1',
        'alergi',
        'riwayat_medis',
        'catatan',
        'aktif',
    ];

    protected $casts = [
        'tanggal_lahir' => 'date',
        'aktif' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: Pasien has many JanjiTemu
     */
    public function janjiTemu(): HasMany
    {
        return $this->hasMany(JanjiTemu::class, 'id_pasien', 'id_pasien');
    }

    /**
     * Relationship: Pasien has many RekamMedis
     */
    public function rekamMedis(): HasMany
    {
        return $this->hasMany(RekamMedis::class, 'id_pasien', 'id_pasien');
    }

    /**
     * Relationship: Pasien has many Tagihan
     */
    public function tagihan(): HasMany
    {
        return $this->hasMany(Tagihan::class, 'id_pasien', 'id_pasien');
    }

    /**
     * Relationship: Pasien has many KomunikasiPasien
     */
    public function komunikasi(): HasMany
    {
        return $this->hasMany(KomunikasiPasien::class, 'id_pasien', 'id_pasien');
    }

    /**
     * Relationship: Pasien has one Keluarga as kontak utama
     */
    public function keluargaSebagaiKontak(): HasOne
    {
        return $this->hasOne(Keluarga::class, 'id_kontak_utama', 'id_pasien');
    }

    /**
     * Relationship: Pasien belongs to many Keluarga through AnggotaKeluarga
     */
    public function keluarga(): HasMany
    {
        return $this->hasMany(AnggotaKeluarga::class, 'id_pasien', 'id_pasien');
    }

    /**
     * Get full name attribute
     */
    public function getNamaLengkapAttribute(): string
    {
        return $this->nama_depan . ' ' . $this->nama_belakang;
    }

    /**
     * Get age attribute
     */
    public function getUmurAttribute(): int
    {
        return $this->tanggal_lahir ? $this->tanggal_lahir->age : 0;
    }

    /**
     * Get full address attribute
     */
    public function getAlamatLengkapAttribute(): string
    {
        $alamat = collect([
            $this->alamat,
            $this->rt_rw ? "RT/RW {$this->rt_rw}" : null,
            $this->kelurahan_desa,
            $this->kecamatan,
            $this->kabupaten_kota,
            $this->provinsi,
            $this->kode_pos,
        ])->filter()->implode(', ');

        return $alamat;
    }

    /**
     * Scope: Active patients only
     */
    public function scopeActive($query)
    {
        return $query->where('aktif', true);
    }

    /**
     * Scope: Search by name or nomor pasien
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('nama_depan', 'like', "%{$search}%")
              ->orWhere('nama_belakang', 'like', "%{$search}%")
              ->orWhere('nomor_pasien', 'like', "%{$search}%")
              ->orWhere('nik', 'like', "%{$search}%");
        });
    }

    /**
     * Check if patient has BPJS
     */
    public function hasBpjs(): bool
    {
        return !empty($this->nomor_bpjs);
    }

    /**
     * Check if patient has insurance
     */
    public function hasAsuransi(): bool
    {
        return !empty($this->penyedia_asuransi) && !empty($this->nomor_asuransi);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('peran', function (Blueprint $table) {
            $table->id('id_peran');
            $table->string('nama_peran')->unique();
            $table->string('deskripsi')->nullable();
            $table->json('hak_akses')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index('nama_peran');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('peran');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jenis_perawatan', function (Blueprint $table) {
            $table->id('id_jenis_perawatan');
            $table->string('kode_perawatan')->unique();
            $table->string('nama_perawatan');
            $table->text('deskripsi')->nullable();
            $table->decimal('harga_standar', 12, 2);
            $table->integer('durasi_estimasi_menit');
            $table->foreignId('id_kategori_perawatan')->constrained('kategori_perawatan', 'id_kategori_perawatan');
            $table->boolean('aktif')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['kode_perawatan', 'aktif']);
            $table->index(['nama_perawatan', 'aktif']);
            $table->index('id_kategori_perawatan');
            $table->index('harga_standar');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jenis_perawatan');
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class HakAkses extends Model
{
    use HasFactory;

    protected $table = 'hak_akses';
    protected $primaryKey = 'id_hak_akses';
    
    public $timestamps = false; // Only has dibuat_pada
    
    protected $fillable = [
        'id_peran',
        'modul',
        'baca',
        'tulis',
        'ubah',
        'hapus',
    ];

    protected $casts = [
        'baca' => 'boolean',
        'tulis' => 'boolean',
        'ubah' => 'boolean',
        'hapus' => 'boolean',
        'dibuat_pada' => 'datetime',
    ];

    /**
     * Relationship: Hak<PERSON>ks<PERSON> belongs to Peran
     */
    public function peran(): BelongsTo
    {
        return $this->belongsTo(Peran::class, 'id_peran', 'id_peran');
    }

    /**
     * Get all permissions for a module
     */
    public function getPermissionsAttribute(): array
    {
        return [
            'baca' => $this->baca,
            'tulis' => $this->tulis,
            'ubah' => $this->ubah,
            'hapus' => $this->hapus,
        ];
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('komunikasi_pasien', function (Blueprint $table) {
            $table->id('id_komunikasi');
            $table->foreignId('id_pasien')->constrained('pasien', 'id_pasien');
            $table->foreignId('id_pengguna')->constrained('pengguna', 'id_pengguna');
            $table->string('jenis_komunikasi');
            $table->string('subjek');
            $table->text('pesan');
            $table->enum('metode_komunikasi', ['email', 'sms', 'whatsapp', 'telepon']);
            $table->enum('status', ['terkirim', 'dibaca', 'dibalas', 'gagal'])->default('terkirim');
            $table->timestamp('tanggal_kirim')->useCurrent();
            $table->timestamp('tanggal_baca')->nullable();
            $table->timestamp('dibuat_pada')->useCurrent();
            
            // Indexes
            $table->index(['id_pasien', 'tanggal_kirim']);
            $table->index(['id_pengguna', 'tanggal_kirim']);
            $table->index(['metode_komunikasi', 'status']);
            $table->index('jenis_komunikasi');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('komunikasi_pasien');
    }
};

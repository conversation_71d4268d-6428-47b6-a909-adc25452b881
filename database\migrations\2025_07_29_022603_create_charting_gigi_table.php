<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('charting_gigi', function (Blueprint $table) {
            $table->id('id_charting');
            $table->foreignId('id_rekam_medis')->constrained('rekam_medis', 'id_rekam_medis')->onDelete('cascade');
            $table->integer('nomor_gigi')->comment('1-32 untuk gigi permanen');
            $table->string('kondisi_gigi');
            $table->text('catatan')->nullable();
            $table->date('tanggal_pemeriksaan');
            $table->timestamps();
            
            // Constraints
            $table->check('nomor_gigi >= 1 AND nomor_gigi <= 32');
            
            // Indexes
            $table->index(['id_rekam_medis', 'nomor_gigi']);
            $table->index(['nomor_gigi', 'tanggal_pemeriksaan']);
            $table->unique(['id_rekam_medis', 'nomor_gigi', 'tanggal_pemeriksaan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('charting_gigi');
    }
};

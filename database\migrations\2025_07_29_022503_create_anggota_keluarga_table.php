<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('anggota_keluarga', function (Blueprint $table) {
            $table->id('id_anggota_keluarga');
            $table->foreignId('id_pasien')->constrained('pasien', 'id_pasien')->onDelete('cascade');
            $table->foreignId('id_keluarga')->constrained('keluarga', 'id_keluarga')->onDelete('cascade');
            $table->string('hubungan_keluarga');
            $table->timestamps();
            
            // Indexes
            $table->index(['id_pasien', 'id_keluarga']);
            $table->unique(['id_pasien', 'id_keluarga']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('anggota_keluarga');
    }
};

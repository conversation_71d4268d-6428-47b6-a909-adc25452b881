<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jenis_gambar', function (Blueprint $table) {
            $table->id('id_jenis_gambar');
            $table->string('nama_jenis')->unique();
            $table->string('deskripsi')->nullable();
            $table->boolean('aktif')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['nama_jenis', 'aktif']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jenis_gambar');
    }
};

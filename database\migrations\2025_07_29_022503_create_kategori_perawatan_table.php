<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kategori_perawatan', function (Blueprint $table) {
            $table->id('id_kategori_perawatan');
            $table->string('nama_kategori')->unique();
            $table->string('deskripsi')->nullable();
            $table->boolean('aktif')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['nama_kategori', 'aktif']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kategori_perawatan');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pasien', function (Blueprint $table) {
            $table->id('id_pasien');
            $table->string('nomor_pasien')->unique();
            $table->string('nama_depan');
            $table->string('nama_belakang');
            $table->date('tanggal_lahir');
            $table->enum('jenis_kelamin', ['L', 'P']);
            $table->string('no_telepon')->nullable();
            $table->string('email')->nullable();
            
            // Alamat lengkap untuk Indonesia
            $table->text('alamat')->nullable();
            $table->string('kota')->nullable();
            $table->string('kode_pos')->nullable();
            $table->string('provinsi')->nullable();
            $table->string('kabupaten_kota')->nullable();
            $table->string('kecamatan')->nullable();
            $table->string('kelurahan_desa')->nullable();
            $table->string('rt_rw')->nullable();
            
            // Identitas dan kontak darurat
            $table->string('nik', 16)->nullable()->comment('Nomor Induk Kependudukan');
            $table->string('nama_kontak_darurat')->nullable();
            $table->string('telepon_kontak_darurat')->nullable();
            
            // Asuransi dan BPJS
            $table->string('penyedia_asuransi')->nullable();
            $table->string('nomor_asuransi')->nullable();
            $table->string('nomor_bpjs', 13)->nullable();
            $table->enum('kelas_bpjs', ['I', 'II', 'III'])->nullable();
            $table->string('status_kepesertaan_bpjs')->nullable();
            $table->string('faskes_tingkat_1')->nullable();
            
            // Medical information
            $table->text('alergi')->nullable();
            $table->text('riwayat_medis')->nullable();
            $table->text('catatan')->nullable();
            
            $table->boolean('aktif')->default(true);
            $table->timestamps();
            
            // Indexes untuk performance
            $table->index(['nomor_pasien', 'aktif']);
            $table->index(['nama_depan', 'nama_belakang']);
            $table->index('nik');
            $table->index('nomor_bpjs');
            $table->index('tanggal_lahir');
            $table->index('jenis_kelamin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pasien');
    }
};

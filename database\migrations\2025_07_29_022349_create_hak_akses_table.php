<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hak_akses', function (Blueprint $table) {
            $table->id('id_hak_akses');
            $table->foreignId('id_peran')->constrained('peran', 'id_peran')->onDelete('cascade');
            $table->string('modul');
            $table->boolean('baca')->default(false);
            $table->boolean('tulis')->default(false);
            $table->boolean('ubah')->default(false);
            $table->boolean('hapus')->default(false);
            $table->timestamp('dibuat_pada')->useCurrent();
            
            // Indexes
            $table->index(['id_peran', 'modul']);
            $table->unique(['id_peran', 'modul']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hak_akses');
    }
};

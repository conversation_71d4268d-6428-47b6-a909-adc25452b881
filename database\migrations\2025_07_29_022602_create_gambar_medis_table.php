<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gambar_medis', function (Blueprint $table) {
            $table->id('id_gambar');
            $table->foreignId('id_rekam_medis')->constrained('rekam_medis', 'id_rekam_medis')->onDelete('cascade');
            $table->foreignId('id_jenis_gambar')->constrained('jenis_gambar', 'id_jenis_gambar');
            $table->string('nama_file');
            $table->string('path_file');
            $table->text('deskripsi')->nullable();
            $table->datetime('tanggal_diambil');
            $table->timestamp('diupload_pada')->useCurrent();
            $table->foreignId('diupload_oleh')->constrained('pengguna', 'id_pengguna');
            
            // Indexes
            $table->index(['id_rekam_medis', 'id_jenis_gambar']);
            $table->index('tanggal_diambil');
            $table->index('diupload_oleh');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gambar_medis');
    }
};

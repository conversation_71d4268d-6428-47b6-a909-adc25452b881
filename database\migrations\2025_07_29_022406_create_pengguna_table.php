<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pengguna', function (Blueprint $table) {
            $table->id('id_pengguna');
            $table->string('username')->unique();
            $table->string('email')->unique();
            $table->string('password_hash');
            $table->string('nama_depan');
            $table->string('nama_belakang');
            $table->string('no_telepon')->nullable();
            $table->foreignId('id_peran')->constrained('peran', 'id_peran');
            
            // Fields untuk compliance dokter Indonesia
            $table->string('nomor_str')->nullable()->comment('Surat Tanda Registrasi');
            $table->string('nomor_sip')->nullable()->comment('Surat Izin Praktik');
            $table->date('expired_str')->nullable();
            $table->date('expired_sip')->nullable();
            
            $table->boolean('aktif')->default(true);
            $table->timestamp('login_terakhir')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['username', 'aktif']);
            $table->index(['email', 'aktif']);
            $table->index('id_peran');
            $table->index('nomor_str');
            $table->index('nomor_sip');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pengguna');
    }
};

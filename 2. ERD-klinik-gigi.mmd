erDiagram
    %% ========================================
    %% ERD KLINIK GIGI - VERSI OPTIMIZED
    %% ========================================
    %%
    %% ENUM CONSTRAINTS:
    %% - jenis_kelamin: 'L', 'P'
    %% - status janji_temu: 'dijadwalkan', 'berlangsung', 'selesai', 'dibatalkan'
    %% - status_tagihan: 'draft', 'dikirim', 'dibayar_sebagian', 'lunas', 'overdue'
    %% - kelas_bpjs: 'I', 'II', 'III'
    %% - nomor_gigi: 1-32 (check constraint)
    %% - metode_komunikasi: 'email', 'sms', 'whatsapp', 'telepon'
    %% - operation (audit): 'INSERT', 'UPDATE', 'DELETE'
    %%
    %% ========================================

    %% Manajemen Pasien
    PASIEN {
        int id_pasien PK
        string nomor_pasien UK
        string nama_depan
        string nama_belakang
        date tanggal_lahir
        string jenis_kelamin
        string no_telepon
        string email
        text alamat
        string kota
        string kode_pos
        string provinsi
        string kabupaten_kota
        string kecamatan
        string kelurahan_desa
        string rt_rw
        string nik
        string nama_kontak_darurat
        string telepon_kontak_darurat
        string penyedia_asuransi
        string nomor_asuransi
        string nomor_bpjs
        string kelas_bpjs
        string status_kepesertaan_bpjs
        string faskes_tingkat_1
        text alergi
        text riwayat_medis
        text catatan
        datetime dibuat_pada
        datetime diperbarui_pada
        boolean aktif
    }

    KELUARGA {
        int id_keluarga PK
        string nama_keluarga
        int id_kontak_utama FK
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    ANGGOTA_KELUARGA {
        int id_pasien FK
        int id_keluarga FK
        string hubungan
        datetime dibuat_pada
    }

    %% Manajemen Pengguna
    PENGGUNA {
        int id_pengguna PK
        string username UK
        string email UK
        string password_hash
        string nama_depan
        string nama_belakang
        string no_telepon
        int id_peran FK
        string nomor_str
        string nomor_sip
        date expired_str
        date expired_sip
        boolean aktif
        datetime login_terakhir
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    PERAN {
        int id_peran PK
        string nama_peran UK
        string deskripsi
        json hak_akses
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    HAK_AKSES {
        int id_hak_akses PK
        int id_peran FK
        string modul
        boolean baca
        boolean tulis
        boolean ubah
        boolean hapus
        datetime dibuat_pada
    }

    %% Manajemen Jadwal & Janji Temu
    JANJI_TEMU {
        int id_janji_temu PK
        string nomor_janji UK
        int id_pasien FK
        int id_dokter FK
        int id_ruang_perawatan FK
        datetime tanggal_janji
        int durasi_menit
        string status
        string jenis_janji
        text catatan
        text keluhan_utama
        decimal estimasi_biaya
        datetime dibuat_pada
        datetime diperbarui_pada
        int dibuat_oleh FK
    }

    PERAWATAN_JANJI {
        int id_perawatan_janji PK
        int id_janji_temu FK
        int id_jenis_perawatan FK
        string status
        text catatan
        decimal biaya
        datetime mulai_perawatan
        datetime selesai_perawatan
    }

    RUANG_PERAWATAN {
        int id_ruang PK
        string nama_ruang UK
        string deskripsi
        boolean aktif
        text peralatan
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    %% Rekam Medis
    REKAM_MEDIS {
        int id_rekam_medis PK
        int id_pasien FK
        int id_janji_temu FK
        int id_dokter FK
        datetime tanggal_kunjungan
        text keluhan_utama
        text pemeriksaan_objektif
        text diagnosis
        text rencana_perawatan
        text catatan_klinis
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    CHARTING_GIGI {
        int id_charting PK
        int id_rekam_medis FK
        int nomor_gigi
        string kondisi_gigi
        text keterangan
        string warna_status
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    JENIS_GAMBAR {
        int id_jenis_gambar PK
        string nama_jenis UK
        string deskripsi
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    GAMBAR_MEDIS {
        int id_gambar PK
        int id_rekam_medis FK
        int id_jenis_gambar FK
        string nama_file
        string path_file
        text deskripsi
        datetime tanggal_diambil
        datetime diupload_pada
        int diupload_oleh FK
    }

    %% Jenis Perawatan
    KATEGORI_PERAWATAN {
        int id_kategori_perawatan PK
        string nama_kategori UK
        string deskripsi
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    JENIS_PERAWATAN {
        int id_jenis_perawatan PK
        string kode_perawatan UK
        string nama_perawatan
        text deskripsi
        decimal harga_standar
        int durasi_estimasi_menit
        int id_kategori_perawatan FK
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    TEMPLATE_PERAWATAN {
        int id_template PK
        string nama_template
        text deskripsi
        text langkah_perawatan
        int durasi_estimasi
        decimal biaya_estimasi
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    TEMPLATE_DETAIL {
        int id_template_detail PK
        int id_template FK
        int id_jenis_perawatan FK
        int urutan
        text instruksi
        boolean wajib
    }

    %% Sistem Pembayaran & Tagihan
    TAGIHAN {
        int id_tagihan PK
        string nomor_tagihan UK
        int id_pasien FK
        int id_janji_temu FK
        decimal subtotal
        decimal diskon
        decimal pajak
        decimal total
        string status_tagihan
        date tanggal_jatuh_tempo
        text catatan
        datetime dibuat_pada
        datetime diperbarui_pada
        int dibuat_oleh FK
    }

    DETAIL_TAGIHAN {
        int id_detail_tagihan PK
        int id_tagihan FK
        int id_jenis_perawatan FK
        string deskripsi
        int kuantitas
        decimal harga_satuan
        decimal subtotal
    }

    PEMBAYARAN {
        int id_pembayaran PK
        string nomor_pembayaran UK
        int id_tagihan FK
        decimal jumlah_pembayaran
        string metode_pembayaran
        string status_pembayaran
        date tanggal_pembayaran
        string nomor_referensi
        text catatan
        datetime dibuat_pada
        int dibuat_oleh FK
    }

    CICILAN {
        int id_cicilan PK
        int id_tagihan FK
        int cicilan_ke
        decimal jumlah_cicilan
        date tanggal_jatuh_tempo
        string status
        date tanggal_pembayaran_aktual
        decimal jumlah_dibayar
        text catatan
    }

    %% Manajemen Inventori
    KATEGORI_INVENTORI {
        int id_kategori PK
        string nama_kategori UK
        string deskripsi
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    INVENTORI {
        int id_inventori PK
        string kode_barang UK
        string nama_barang
        int id_kategori FK
        string satuan
        decimal harga_beli
        decimal harga_jual
        int stok_minimum
        int stok_saat_ini
        date tanggal_kadaluarsa
        int id_supplier FK
        text deskripsi
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    SUPPLIER {
        int id_supplier PK
        string nama_supplier
        string kontak_person
        string no_telepon
        string email
        text alamat
        string kota
        text catatan
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    PEMBELIAN {
        int id_pembelian PK
        string nomor_pembelian UK
        int id_supplier FK
        date tanggal_pembelian
        decimal total_pembelian
        string status
        date tanggal_diterima
        text catatan
        datetime dibuat_pada
        int dibuat_oleh FK
    }

    DETAIL_PEMBELIAN {
        int id_detail_pembelian PK
        int id_pembelian FK
        int id_inventori FK
        int kuantitas
        decimal harga_satuan
        decimal subtotal
        date tanggal_kadaluarsa
    }

    PENGGUNAAN_INVENTORI {
        int id_penggunaan PK
        int id_inventori FK
        int id_janji_temu FK
        int kuantitas_digunakan
        datetime tanggal_penggunaan
        int digunakan_oleh FK
        text catatan
    }

    %% Sistem Rujukan
    RUJUKAN {
        int id_rujukan PK
        string nomor_rujukan UK
        int id_pasien FK
        int id_dokter_perujuk FK
        string nama_dokter_tujuan
        string spesialis_tujuan
        string nama_klinik_tujuan
        text alasan_rujukan
        text diagnosis_sementara
        date tanggal_rujukan
        string status
        text feedback
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    %% Resep Obat
    RESEP {
        int id_resep PK
        string nomor_resep UK
        int id_pasien FK
        int id_dokter FK
        int id_janji_temu FK
        date tanggal_resep
        text instruksi_umum
        string status
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    OBAT {
        int id_obat PK
        string kode_obat UK
        string nama_obat
        string jenis_obat
        string kekuatan
        string satuan
        text indikasi
        text kontraindikasi
        text efek_samping
        boolean aktif
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    DETAIL_RESEP {
        int id_detail_resep PK
        int id_resep FK
        int id_obat FK
        string dosis
        string frekuensi
        int durasi_hari
        string cara_pakai
        text catatan_khusus
    }

    %% Sistem Reminder & Notifikasi
    REMINDER {
        int id_reminder PK
        int id_pasien FK
        string jenis_reminder
        string judul
        text pesan
        datetime jadwal_kirim
        string metode_kirim
        string status
        datetime dikirim_pada
        int percobaan_kirim
        datetime dibuat_pada
    }

    RECALL_PASIEN {
        int id_recall PK
        int id_pasien FK
        int id_jenis_perawatan FK
        date tanggal_recall_terakhir
        date tanggal_recall_berikutnya
        string status
        text catatan
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    KOMUNIKASI_PASIEN {
        int id_komunikasi PK
        int id_pasien FK
        int id_pengguna FK
        string jenis_komunikasi
        string subjek
        text pesan
        string metode_komunikasi
        string status
        datetime tanggal_kirim
        datetime tanggal_baca
        datetime dibuat_pada
    }

    %% Sistem Laboratorium
    ORDER_LAB {
        int id_order_lab PK
        string nomor_order UK
        int id_pasien FK
        int id_dokter FK
        int id_janji_temu FK
        string jenis_pemeriksaan
        string lab_tujuan
        date tanggal_order
        date tanggal_estimasi_selesai
        string status
        decimal biaya
        text instruksi_khusus
        datetime dibuat_pada
        datetime diperbarui_pada
    }

    HASIL_LAB {
        int id_hasil_lab PK
        int id_order_lab FK
        text hasil_pemeriksaan
        string nama_file_hasil
        string path_file_hasil
        date tanggal_hasil
        text catatan_lab
        datetime diupload_pada
        int diupload_oleh FK
    }

    %% Sistem Audit
    AUDIT_LOG {
        int id_audit PK
        string table_name
        string operation
        json old_values
        json new_values
        int user_id FK
        string ip_address
        string user_agent
        datetime timestamp
    }



    %% Relationships
    PASIEN ||--o{ ANGGOTA_KELUARGA : memiliki
    KELUARGA ||--o{ ANGGOTA_KELUARGA : terdiri_dari
    PASIEN ||--o| KELUARGA : id_kontak_utama

    PENGGUNA }o--|| PERAN : memiliki
    PERAN ||--o{ HAK_AKSES : memiliki
    
    PASIEN ||--o{ JANJI_TEMU : membuat
    PENGGUNA ||--o{ JANJI_TEMU : id_dokter
    PENGGUNA ||--o{ JANJI_TEMU : dibuat_oleh
    RUANG_PERAWATAN ||--o{ JANJI_TEMU : digunakan
    
    JANJI_TEMU ||--o{ PERAWATAN_JANJI : memiliki
    JENIS_PERAWATAN ||--o{ PERAWATAN_JANJI : dilakukan
    
    PASIEN ||--o{ REKAM_MEDIS : memiliki
    JANJI_TEMU ||--o| REKAM_MEDIS : menghasilkan
    PENGGUNA ||--o{ REKAM_MEDIS : id_dokter
    
    REKAM_MEDIS ||--o{ CHARTING_GIGI : memiliki
    REKAM_MEDIS ||--o{ GAMBAR_MEDIS : memiliki
    JENIS_GAMBAR ||--o{ GAMBAR_MEDIS : mengkategorikan
    PENGGUNA ||--o{ GAMBAR_MEDIS : diupload_oleh
    
    TEMPLATE_PERAWATAN ||--o{ TEMPLATE_DETAIL : memiliki
    JENIS_PERAWATAN ||--o{ TEMPLATE_DETAIL : termasuk
    KATEGORI_PERAWATAN ||--o{ JENIS_PERAWATAN : mengkategorikan
    
    PASIEN ||--o{ TAGIHAN : memiliki
    JANJI_TEMU ||--o| TAGIHAN : menghasilkan
    PENGGUNA ||--o{ TAGIHAN : dibuat_oleh
    
    TAGIHAN ||--o{ DETAIL_TAGIHAN : memiliki
    JENIS_PERAWATAN ||--o{ DETAIL_TAGIHAN : ditagihkan
    
    TAGIHAN ||--o{ PEMBAYARAN : memiliki
    PENGGUNA ||--o{ PEMBAYARAN : dibuat_oleh
    
    TAGIHAN ||--o{ CICILAN : memiliki
    
    KATEGORI_INVENTORI ||--o{ INVENTORI : mengkategorikan
    SUPPLIER ||--o{ INVENTORI : memasok
    
    SUPPLIER ||--o{ PEMBELIAN : melakukan
    PENGGUNA ||--o{ PEMBELIAN : dibuat_oleh
    
    PEMBELIAN ||--o{ DETAIL_PEMBELIAN : memiliki
    INVENTORI ||--o{ DETAIL_PEMBELIAN : dibeli
    
    INVENTORI ||--o{ PENGGUNAAN_INVENTORI : digunakan
    JANJI_TEMU ||--o{ PENGGUNAAN_INVENTORI : menggunakan
    PENGGUNA ||--o{ PENGGUNAAN_INVENTORI : digunakan_oleh
    
    PASIEN ||--o{ RUJUKAN : memiliki
    PENGGUNA ||--o{ RUJUKAN : id_dokter_perujuk
    
    PASIEN ||--o{ RESEP : memiliki
    PENGGUNA ||--o{ RESEP : id_dokter
    JANJI_TEMU ||--o{ RESEP : menghasilkan
    
    RESEP ||--o{ DETAIL_RESEP : memiliki
    OBAT ||--o{ DETAIL_RESEP : diresepkan
    
    PASIEN ||--o{ REMINDER : menerima
    
    PASIEN ||--o{ RECALL_PASIEN : memiliki
    JENIS_PERAWATAN ||--o{ RECALL_PASIEN : untuk
    
    PASIEN ||--o{ ORDER_LAB : memiliki
    PENGGUNA ||--o{ ORDER_LAB : id_dokter
    JANJI_TEMU ||--o{ ORDER_LAB : menghasilkan
    
    ORDER_LAB ||--o{ HASIL_LAB : memiliki
    PENGGUNA ||--o{ HASIL_LAB : diupload_oleh

    PASIEN ||--o{ KOMUNIKASI_PASIEN : menerima
    PENGGUNA ||--o{ KOMUNIKASI_PASIEN : mengirim

    PENGGUNA ||--o{ AUDIT_LOG : melakukan
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('detail_tagihan', function (Blueprint $table) {
            $table->id('id_detail_tagihan');
            $table->foreignId('id_tagihan')->constrained('tagihan', 'id_tagihan')->onDelete('cascade');
            $table->foreignId('id_jenis_perawatan')->constrained('jenis_perawatan', 'id_jenis_perawatan');
            $table->string('deskripsi_item');
            $table->integer('kuantitas')->default(1);
            $table->decimal('harga_satuan', 12, 2);
            $table->decimal('total_harga', 12, 2);
            $table->timestamps();
            
            // Indexes
            $table->index(['id_tagihan', 'id_jenis_perawatan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('detail_tagihan');
    }
};

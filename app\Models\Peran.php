<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Peran extends Model
{
    use HasFactory;

    protected $table = 'peran';
    protected $primaryKey = 'id_peran';
    
    protected $fillable = [
        'nama_peran',
        'deskripsi',
        'hak_akses',
    ];

    protected $casts = [
        'hak_akses' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: <PERSON><PERSON> has many Pengguna
     */
    public function pengguna(): HasMany
    {
        return $this->hasMany(Pengguna::class, 'id_peran', 'id_peran');
    }

    /**
     * Relationship: <PERSON><PERSON> has many HakAkses
     */
    public function hakAkses(): HasMany
    {
        return $this->hasMany(HakAkses::class, 'id_peran', 'id_peran');
    }

    /**
     * Scope: Active roles only
     */
    public function scopeActive($query)
    {
        return $query->where('aktif', true);
    }

    /**
     * Check if role has specific permission
     */
    public function hasPermission(string $module, string $action): bool
    {
        return $this->hakAkses()
            ->where('modul', $module)
            ->where($action, true)
            ->exists();
    }
}

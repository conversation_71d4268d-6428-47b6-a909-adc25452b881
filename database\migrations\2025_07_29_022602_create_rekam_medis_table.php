<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rekam_medis', function (Blueprint $table) {
            $table->id('id_rekam_medis');
            $table->foreignId('id_pasien')->constrained('pasien', 'id_pasien');
            $table->foreignId('id_janji_temu')->constrained('janji_temu', 'id_janji_temu');
            $table->foreignId('id_dokter')->constrained('pengguna', 'id_pengguna');
            
            $table->date('tanggal_kunjungan');
            
            // SOAP Notes
            $table->text('subjective')->nullable()->comment('Keluhan pasien');
            $table->text('objective')->nullable()->comment('Pemeriksaan objektif');
            $table->text('assessment')->nullable()->comment('Diagnosis');
            $table->text('plan')->nullable()->comment('Rencana perawatan');
            
            $table->text('anamnesis')->nullable();
            $table->text('pemeriksaan_fisik')->nullable();
            $table->text('diagnosis')->nullable();
            $table->text('tindakan_perawatan')->nullable();
            $table->text('resep_obat')->nullable();
            $table->text('instruksi_pasien')->nullable();
            $table->text('catatan_tambahan')->nullable();
            
            $table->timestamps();
            
            // Indexes untuk performance
            $table->index(['id_pasien', 'tanggal_kunjungan']);
            $table->index(['id_dokter', 'tanggal_kunjungan']);
            $table->index('id_janji_temu');
            $table->index('tanggal_kunjungan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rekam_medis');
    }
};

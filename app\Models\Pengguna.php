<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Pengguna extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $table = 'pengguna';
    protected $primaryKey = 'id_pengguna';
    
    protected $fillable = [
        'username',
        'email',
        'password_hash',
        'nama_depan',
        'nama_belakang',
        'no_telepon',
        'id_peran',
        'nomor_str',
        'nomor_sip',
        'expired_str',
        'expired_sip',
        'aktif',
        'login_terakhir',
    ];

    protected $hidden = [
        'password_hash',
        'remember_token',
    ];

    protected $casts = [
        'aktif' => 'boolean',
        'login_terakhir' => 'datetime',
        'expired_str' => 'date',
        'expired_sip' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the password attribute name for authentication
     */
    public function getAuthPassword()
    {
        return $this->password_hash;
    }

    /**
     * Relationship: Pengguna belongs to Peran
     */
    public function peran(): BelongsTo
    {
        return $this->belongsTo(Peran::class, 'id_peran', 'id_peran');
    }

    /**
     * Relationship: Pengguna has many JanjiTemu as dokter
     */
    public function janjiTemuSebagaiDokter(): HasMany
    {
        return $this->hasMany(JanjiTemu::class, 'id_dokter', 'id_pengguna');
    }

    /**
     * Relationship: Pengguna has many RekamMedis as dokter
     */
    public function rekamMedisSebagaiDokter(): HasMany
    {
        return $this->hasMany(RekamMedis::class, 'id_dokter', 'id_pengguna');
    }

    /**
     * Relationship: Pengguna has many KomunikasiPasien
     */
    public function komunikasiPasien(): HasMany
    {
        return $this->hasMany(KomunikasiPasien::class, 'id_pengguna', 'id_pengguna');
    }

    /**
     * Relationship: Pengguna has many AuditLog
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class, 'user_id', 'id_pengguna');
    }

    /**
     * Get full name attribute
     */
    public function getNamaLengkapAttribute(): string
    {
        return $this->nama_depan . ' ' . $this->nama_belakang;
    }

    /**
     * Scope: Active users only
     */
    public function scopeActive($query)
    {
        return $query->where('aktif', true);
    }

    /**
     * Scope: Doctors only
     */
    public function scopeDokter($query)
    {
        return $query->whereHas('peran', function ($q) {
            $q->where('nama_peran', 'like', '%dokter%');
        });
    }

    /**
     * Check if user has specific permission
     */
    public function hasPermission(string $module, string $action): bool
    {
        return $this->peran?->hasPermission($module, $action) ?? false;
    }

    /**
     * Check if STR/SIP is valid
     */
    public function isStrValid(): bool
    {
        return $this->expired_str && $this->expired_str->isFuture();
    }

    public function isSipValid(): bool
    {
        return $this->expired_sip && $this->expired_sip->isFuture();
    }
}

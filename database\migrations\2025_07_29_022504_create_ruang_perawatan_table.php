<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ruang_perawatan', function (Blueprint $table) {
            $table->id('id_ruang');
            $table->string('nama_ruang')->unique();
            $table->string('lokasi')->nullable();
            $table->text('deskripsi')->nullable();
            $table->enum('status', ['tersedia', 'terpakai', 'maintenance'])->default('tersedia');
            $table->boolean('aktif')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['nama_ruang', 'aktif']);
            $table->index(['status', 'aktif']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ruang_perawatan');
    }
};

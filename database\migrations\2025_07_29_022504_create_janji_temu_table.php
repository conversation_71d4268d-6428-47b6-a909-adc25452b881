<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('janji_temu', function (Blueprint $table) {
            $table->id('id_janji_temu');
            $table->foreignId('id_pasien')->constrained('pasien', 'id_pasien');
            $table->foreignId('id_dokter')->constrained('pengguna', 'id_pengguna');
            $table->foreignId('id_jenis_perawatan')->constrained('jenis_perawatan', 'id_jenis_perawatan');
            $table->foreignId('id_ruang')->nullable()->constrained('ruang_perawatan', 'id_ruang');
            
            $table->datetime('tanggal_janji');
            $table->time('jam_mulai');
            $table->time('jam_selesai');
            $table->enum('status', ['dijadwalkan', 'berlangsung', 'selesai', 'dibatalkan'])->default('dijadwalkan');
            $table->decimal('estimasi_biaya', 12, 2)->nullable();
            $table->text('catatan')->nullable();
            $table->text('alasan_batal')->nullable();
            
            $table->timestamps();
            
            // Indexes untuk performance
            $table->index(['id_pasien', 'tanggal_janji']);
            $table->index(['id_dokter', 'tanggal_janji']);
            $table->index(['tanggal_janji', 'status']);
            $table->index(['id_ruang', 'tanggal_janji']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('janji_temu');
    }
};

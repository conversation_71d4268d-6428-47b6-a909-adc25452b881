<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tagihan', function (Blueprint $table) {
            $table->id('id_tagihan');
            $table->string('nomor_tagihan')->unique();
            $table->foreignId('id_pasien')->constrained('pasien', 'id_pasien');
            $table->foreignId('id_janji_temu')->constrained('janji_temu', 'id_janji_temu');
            
            $table->date('tanggal_tagihan');
            $table->date('tanggal_jatuh_tempo');
            $table->decimal('subtotal', 12, 2);
            $table->decimal('diskon', 12, 2)->default(0);
            $table->decimal('pajak', 12, 2)->default(0);
            $table->decimal('total_tagihan', 12, 2);
            $table->decimal('jumlah_dibayar', 12, 2)->default(0);
            $table->decimal('sisa_tagihan', 12, 2);
            
            $table->enum('status_tagihan', ['draft', 'dikirim', 'dibayar_sebagian', 'lunas', 'overdue'])->default('draft');
            $table->text('catatan')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['nomor_tagihan']);
            $table->index(['id_pasien', 'status_tagihan']);
            $table->index(['status_tagihan', 'tanggal_jatuh_tempo']);
            $table->index('tanggal_tagihan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tagihan');
    }
};
